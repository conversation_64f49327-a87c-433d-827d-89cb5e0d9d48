"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      currentWallpaper: null,
      recentWallpapers: [],
      loading: false,
      showDetail: false,
      detailWallpaper: null,
      showDownloadMenu: false,
      downloadWallpaperTarget: null,
      resolutions: ["1920x1080", "2560x1440", "3840x2160"],
      usageTips: [
        "每日更新Bing搜索首页的精美壁纸",
        "支持多种分辨率下载，适配不同设备",
        "所有图片均为高质量摄影作品",
        "可查看最近一周的历史壁纸",
        "一键下载，方便设置为桌面壁纸"
      ]
    };
  },
  onLoad() {
    this.initializeWallpapers();
  },
  methods: {
    // 初始化壁纸数据
    initializeWallpapers() {
      const mockWallpapers = this.generateMockWallpapers();
      this.currentWallpaper = mockWallpapers[0];
      this.recentWallpapers = mockWallpapers;
    },
    // 生成模拟壁纸数据
    generateMockWallpapers() {
      const mockData = [
        {
          date: (/* @__PURE__ */ new Date()).toISOString().split("T")[0],
          title: "阿尔卑斯山的日出",
          description: "瑞士阿尔卑斯山脉在晨曦中展现出壮丽的景色，金色的阳光洒向雪山顶峰。",
          url: "https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=1920&h=1080&fit=crop",
          location: "瑞士阿尔卑斯山"
        },
        {
          date: new Date(Date.now() - 864e5).toISOString().split("T")[0],
          title: "挪威峡湾风光",
          description: "挪威的峡湾在蓝色天空下展现出令人惊叹的自然美景。",
          url: "https://images.unsplash.com/photo-1506744038136-46273834b3fb?w=1920&h=1080&fit=crop",
          location: "挪威峡湾"
        },
        {
          date: new Date(Date.now() - 1728e5).toISOString().split("T")[0],
          title: "樱花盛开的季节",
          description: "春天的樱花树下，粉色的花瓣如雪花般飘落。",
          url: "https://images.unsplash.com/photo-1522383225653-ed111181a951?w=1920&h=1080&fit=crop",
          location: "日本京都"
        },
        {
          date: new Date(Date.now() - 2592e5).toISOString().split("T")[0],
          title: "撒哈拉沙漠的星空",
          description: "在撒哈拉沙漠的夜晚，满天繁星点亮了整个天空。",
          url: "https://images.unsplash.com/photo-1470813740244-df37b8c1edcb?w=1920&h=1080&fit=crop",
          location: "撒哈拉沙漠"
        },
        {
          date: new Date(Date.now() - 3456e5).toISOString().split("T")[0],
          title: "冰岛的蓝色冰川",
          description: "冰岛瓦特纳冰川国家公园的蓝色冰洞，展现大自然的神奇魅力。",
          url: "https://images.unsplash.com/photo-1531366936337-7c912a4589a7?w=1920&h=1080&fit=crop",
          location: "冰岛瓦特纳冰川"
        },
        {
          date: new Date(Date.now() - 432e6).toISOString().split("T")[0],
          title: "托斯卡纳的田园风光",
          description: "意大利托斯卡纳地区的丘陵地带，绿色的葡萄园延伸到地平线。",
          url: "https://images.unsplash.com/photo-1523712999610-f77fbcfc3843?w=1920&h=1080&fit=crop",
          location: "意大利托斯卡纳"
        },
        {
          date: new Date(Date.now() - 5184e5).toISOString().split("T")[0],
          title: "马尔代夫的蓝色泻湖",
          description: "马尔代夫清澈的海水和白色的沙滩构成了完美的热带天堂。",
          url: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&h=1080&fit=crop",
          location: "马尔代夫"
        }
      ];
      return mockData;
    },
    // 返回上一页
    navigateBack() {
      common_vendor.index.navigateBack();
    },
    // 刷新壁纸
    refreshWallpapers() {
      if (this.loading)
        return;
      this.loading = true;
      setTimeout(() => {
        const newWallpapers = this.generateMockWallpapers();
        const idx = Math.floor(Math.random() * newWallpapers.length);
        this.currentWallpaper = newWallpapers[idx];
        this.recentWallpapers = newWallpapers;
        this.loading = false;
        common_vendor.index.showToast({
          title: "刷新成功",
          icon: "success",
          duration: 2e3
        });
      }, 1e3);
    },
    // 显示下载选项
    showDownloadOptions(wallpaper) {
      this.downloadWallpaperTarget = wallpaper;
      this.showDownloadMenu = true;
    },
    // 关闭下载菜单
    closeDownloadMenu() {
      this.showDownloadMenu = false;
      this.downloadWallpaperTarget = null;
    },
    // 确认下载
    confirmDownload(resolution) {
      if (this.downloadWallpaperTarget) {
        this.downloadWallpaper(this.downloadWallpaperTarget, resolution);
      }
      this.closeDownloadMenu();
    },
    // 下载壁纸
    downloadWallpaper(wallpaper, resolution = "1920x1080") {
      common_vendor.index.showLoading({
        title: "下载中..."
      });
      common_vendor.index.downloadFile({
        url: wallpaper.url,
        success: (res) => {
          if (res.statusCode === 200) {
            common_vendor.index.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                common_vendor.index.hideLoading();
                common_vendor.index.showToast({
                  title: "保存成功",
                  icon: "success",
                  duration: 2e3
                });
              },
              fail: (error) => {
                common_vendor.index.hideLoading();
                common_vendor.index.__f__("error", "at pages/tools/bing-daily-wallpaper.vue:331", "保存失败:", error);
                common_vendor.index.showToast({
                  title: "保存失败",
                  icon: "error",
                  duration: 2e3
                });
              }
            });
          }
        },
        fail: (error) => {
          common_vendor.index.hideLoading();
          common_vendor.index.__f__("error", "at pages/tools/bing-daily-wallpaper.vue:343", "下载失败:", error);
          common_vendor.index.showToast({
            title: "下载失败",
            icon: "error",
            duration: 2e3
          });
        }
      });
    },
    // 显示壁纸详情
    showWallpaperDetail(wallpaper) {
      this.detailWallpaper = wallpaper;
      this.showDetail = true;
    },
    // 关闭详情
    closeDetail() {
      this.showDetail = false;
      this.detailWallpaper = null;
    },
    // 获取分辨率描述
    getResolutionDesc(resolution) {
      const descriptions = {
        "1920x1080": "全高清",
        "2560x1440": "2K",
        "3840x2160": "4K"
      };
      return descriptions[resolution] || "";
    },
    // 图片加载错误处理
    onImageError(e) {
      common_vendor.index.__f__("error", "at pages/tools/bing-daily-wallpaper.vue:377", "图片加载失败:", e);
    },
    // 处理位置点击
    onLocationClick(location) {
    }
  }
};
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_polyline = common_vendor.resolveComponent("polyline");
  const _component_line = common_vendor.resolveComponent("line");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_circle = common_vendor.resolveComponent("circle");
  (_component_path + _component_polyline + _component_line + _component_svg + _component_circle)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.currentWallpaper
  }, $data.currentWallpaper ? {
    b: common_vendor.t($data.currentWallpaper.date),
    c: common_vendor.o((...args) => $options.refreshWallpapers && $options.refreshWallpapers(...args)),
    d: $data.loading,
    e: $data.currentWallpaper.url,
    f: $data.currentWallpaper.title,
    g: common_vendor.o((...args) => $options.onImageError && $options.onImageError(...args)),
    h: common_vendor.o(($event) => $options.showWallpaperDetail($data.currentWallpaper)),
    i: common_vendor.p({
      d: "M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"
    }),
    j: common_vendor.p({
      points: "7 10 12 15 17 10"
    }),
    k: common_vendor.p({
      x1: "12",
      y1: "15",
      x2: "12",
      y2: "3"
    }),
    l: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "32",
      height: "32",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2.4",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    m: common_vendor.o(($event) => $options.downloadWallpaper($data.currentWallpaper)),
    n: common_vendor.t($data.currentWallpaper.title),
    o: common_vendor.t($data.currentWallpaper.description),
    p: common_vendor.p({
      cx: "12",
      cy: "10",
      r: "3"
    }),
    q: common_vendor.p({
      d: "M12 2a8 8 0 0 1 8 8c0 7-8 12-8 12S4 17 4 10a8 8 0 0 1 8-8z"
    }),
    r: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "18",
      height: "18",
      fill: "none",
      stroke: "#1976d2",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    s: common_vendor.t($data.currentWallpaper.location),
    t: common_vendor.o(($event) => $options.onLocationClick($data.currentWallpaper.location))
  } : {}, {
    v: common_vendor.f($data.recentWallpapers.slice(1), (wallpaper, index, i0) => {
      return {
        a: wallpaper.url,
        b: wallpaper.title,
        c: common_vendor.o((...args) => $options.onImageError && $options.onImageError(...args), index),
        d: common_vendor.t(wallpaper.title),
        e: common_vendor.t(wallpaper.date),
        f: "b1ea4072-8-" + i0 + "," + ("b1ea4072-7-" + i0),
        g: "b1ea4072-9-" + i0 + "," + ("b1ea4072-7-" + i0),
        h: "b1ea4072-7-" + i0,
        i: common_vendor.t(wallpaper.location),
        j: common_vendor.o(($event) => $options.onLocationClick(wallpaper.location), index),
        k: "b1ea4072-11-" + i0 + "," + ("b1ea4072-10-" + i0),
        l: "b1ea4072-12-" + i0 + "," + ("b1ea4072-10-" + i0),
        m: "b1ea4072-13-" + i0 + "," + ("b1ea4072-10-" + i0),
        n: "b1ea4072-10-" + i0,
        o: common_vendor.o(($event) => $options.downloadWallpaper(wallpaper, "1920x1080"), index),
        p: index,
        q: common_vendor.o(($event) => $options.showWallpaperDetail(wallpaper), index)
      };
    }),
    w: common_vendor.p({
      cx: "12",
      cy: "10",
      r: "3"
    }),
    x: common_vendor.p({
      d: "M12 2a8 8 0 0 1 8 8c0 7-8 12-8 12S4 17 4 10a8 8 0 0 1 8-8z"
    }),
    y: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "16",
      height: "16",
      fill: "none",
      stroke: "#1976d2",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    z: common_vendor.p({
      d: "M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"
    }),
    A: common_vendor.p({
      points: "7 10 12 15 17 10"
    }),
    B: common_vendor.p({
      x1: "12",
      y1: "15",
      x2: "12",
      y2: "3"
    }),
    C: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "36",
      height: "36",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2.4",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    D: common_vendor.f($data.usageTips, (tip, index, i0) => {
      return {
        a: common_vendor.t(tip),
        b: index
      };
    }),
    E: $data.showDetail
  }, $data.showDetail ? {
    F: common_vendor.t($data.detailWallpaper.title),
    G: common_vendor.o((...args) => $options.closeDetail && $options.closeDetail(...args)),
    H: $data.detailWallpaper.url,
    I: $data.detailWallpaper.title,
    J: common_vendor.t($data.detailWallpaper.description),
    K: common_vendor.p({
      cx: "12",
      cy: "10",
      r: "3"
    }),
    L: common_vendor.p({
      d: "M12 2a8 8 0 0 1 8 8c0 7-8 12-8 12S4 17 4 10a8 8 0 0 1 8-8z"
    }),
    M: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "16",
      height: "16",
      fill: "none",
      stroke: "#1976d2",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    N: common_vendor.t($data.detailWallpaper.location),
    O: common_vendor.o(($event) => $options.onLocationClick($data.detailWallpaper.location)),
    P: common_vendor.t($data.detailWallpaper.date),
    Q: common_vendor.f($data.resolutions, (resolution, k0, i0) => {
      return {
        a: "b1ea4072-18-" + i0 + "," + ("b1ea4072-17-" + i0),
        b: "b1ea4072-19-" + i0 + "," + ("b1ea4072-17-" + i0),
        c: "b1ea4072-20-" + i0 + "," + ("b1ea4072-17-" + i0),
        d: "b1ea4072-17-" + i0,
        e: common_vendor.t(resolution),
        f: resolution,
        g: common_vendor.o(($event) => $options.downloadWallpaper($data.detailWallpaper, resolution), resolution)
      };
    }),
    R: common_vendor.p({
      d: "M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"
    }),
    S: common_vendor.p({
      points: "7 10 12 15 17 10"
    }),
    T: common_vendor.p({
      x1: "12",
      y1: "15",
      x2: "12",
      y2: "3"
    }),
    U: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "22",
      height: "22",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    V: common_vendor.o((...args) => $options.closeDetail && $options.closeDetail(...args))
  } : {}, {
    W: $data.showDownloadMenu
  }, $data.showDownloadMenu ? {
    X: common_vendor.o((...args) => $options.closeDownloadMenu && $options.closeDownloadMenu(...args)),
    Y: common_vendor.f($data.resolutions, (resolution, k0, i0) => {
      return {
        a: common_vendor.t(resolution),
        b: common_vendor.t($options.getResolutionDesc(resolution)),
        c: resolution,
        d: common_vendor.o(($event) => $options.confirmDownload(resolution), resolution)
      };
    }),
    Z: common_vendor.o((...args) => $options.closeDownloadMenu && $options.closeDownloadMenu(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-b1ea4072"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/bing-daily-wallpaper.js.map
