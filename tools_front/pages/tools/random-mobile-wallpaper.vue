<template>
  <view class="random-mobile-wallpaper">
    <!-- 分类筛选 -->
    <view class="filter-section">
      <view class="filter-header">
        <text class="filter-icon">📱</text>
        <text class="filter-title">分类筛选</text>
      </view>
      <scroll-view
        class="category-scroll"
        scroll-x
        show-scrollbar="false"
        enhanced
      >
        <view class="category-list">
          <view
            v-for="category in categories"
            :key="category.id"
            class="category-btn"
            :class="{ active: selectedCategory.id === category.id }"
            @tap="selectCategory(category)"
          >
            <text class="category-icon">{{ getCategoryIcon(category.id) }}</text>
            <text class="category-name">{{ category.name }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 壁纸展示 -->
    <view class="wallpaper-section">
      <view class="section-title">
        {{ selectedCategory.name || '全部壁纸' }}
        <text class="wallpaper-count">({{ wallpaperBatch.length }})</text>
      </view>

      <!-- 双列瀑布流布局 -->
      <view class="wallpaper-grid">
        <view class="wallpaper-column">
          <view
            class="wallpaper-card"
            v-for="(wallpaper, index) in leftColumnWallpapers"
            :key="wallpaper.id"
            :style="{ marginTop: index === 0 ? '0' : '24rpx' }"
          >
            <image
              :src="wallpaper.url"
              :alt="wallpaper.title"
              class="wallpaper-img"
              mode="aspectFill"
              @load="imageLoaded = true"
              @error="imageError = true"
            />
            <view class="wallpaper-overlay">
              <view class="wallpaper-info">
                <text class="wallpaper-title">{{ wallpaper.title }}</text>
                <text class="wallpaper-resolution">{{ selectedResolution.width }}×{{ selectedResolution.height }}</text>
              </view>
              <view class="wallpaper-tags">
                <text
                  v-for="(tag, tagIndex) in wallpaper.tags.slice(0, 2)"
                  :key="tagIndex"
                  class="tag"
                >{{ tag }}</text>
              </view>
            </view>
            <view class="action-buttons">
              <view
                class="action-btn favorite"
                :class="{ active: favorites.includes(wallpaper.id) }"
                @tap="toggleFavorite(wallpaper.id)"
              >
                <text class="action-icon">♥</text>
              </view>
              <view
                class="action-btn download"
                @tap="downloadWallpaper(wallpaper)"
              >
                <text class="action-icon">↓</text>
              </view>
            </view>
          </view>
        </view>

        <view class="wallpaper-column">
          <view
            class="wallpaper-card"
            v-for="(wallpaper, index) in rightColumnWallpapers"
            :key="wallpaper.id"
            :style="{ marginTop: index === 0 ? '40rpx' : '24rpx' }"
          >
            <image
              :src="wallpaper.url"
              :alt="wallpaper.title"
              class="wallpaper-img"
              mode="aspectFill"
              @load="imageLoaded = true"
              @error="imageError = true"
            />
            <view class="wallpaper-overlay">
              <view class="wallpaper-info">
                <text class="wallpaper-title">{{ wallpaper.title }}</text>
                <text class="wallpaper-resolution">{{ selectedResolution.width }}×{{ selectedResolution.height }}</text>
              </view>
              <view class="wallpaper-tags">
                <text
                  v-for="(tag, tagIndex) in wallpaper.tags.slice(0, 2)"
                  :key="tagIndex"
                  class="tag"
                >{{ tag }}</text>
              </view>
            </view>
            <view class="action-buttons">
              <view
                class="action-btn favorite"
                :class="{ active: favorites.includes(wallpaper.id) }"
                @tap="toggleFavorite(wallpaper.id)"
              >
                <text class="action-icon">♥</text>
              </view>
              <view
                class="action-btn download"
                @tap="downloadWallpaper(wallpaper)"
              >
                <text class="action-icon">↓</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ToolService } from '../../utils/toolService.js'
import { showSuccess, showError, showLoading, hideLoading } from '../../utils/index.js'

const toolService = new ToolService()

const loading = ref(false)
const batchLoading = ref(false)
const imageLoaded = ref(true)
const imageError = ref(false)

const selectedCategory = ref({ id: 'nature', name: '自然风光' })
const selectedResolution = ref({ id: '750x1334', name: 'iPhone 6/7/8', width: 750, height: 1334 })

const categories = [
  { id: 'all', name: '全部' },
  { id: 'nature', name: '自然风光' },
  { id: 'city', name: '城市建筑' },
  { id: 'abstract', name: '抽象艺术' },
  { id: 'space', name: '宇宙星空' },
  { id: 'animal', name: '动物世界' },
  { id: 'tech', name: '科技数码' },
  { id: 'art', name: '艺术创作' },
  { id: 'minimal', name: '简约风格' }
]

const resolutions = [
  { id: '750x1334', name: 'iPhone 6/7/8', width: 750, height: 1334 },
  { id: '828x1792', name: 'iPhone XR', width: 828, height: 1792 },
  { id: '1125x2436', name: 'iPhone X/XS', width: 1125, height: 2436 },
  { id: '1242x2688', name: 'iPhone XS Max', width: 1242, height: 2688 },
  { id: '1080x1920', name: 'Android FHD', width: 1080, height: 1920 }
]

const wallpaperBatch = ref([])
const favorites = ref([])
const downloadHistory = ref([])

// 计算属性：将壁纸分为左右两列
const leftColumnWallpapers = computed(() => {
  return wallpaperBatch.value.filter((_, index) => index % 2 === 0)
})

const rightColumnWallpapers = computed(() => {
  return wallpaperBatch.value.filter((_, index) => index % 2 === 1)
})

const selectCategory = async (category) => {
  selectedCategory.value = category
  // 选择分类后立即更新壁纸列表
  await generateWallpaperBatch(category.id)
}

// 生成壁纸批次
const generateWallpaperBatch = async (categoryId = 'all') => {
  batchLoading.value = true

  try {
    // 调用后端API
    const result = await toolService.getRandomMobileWallpaper({
      category: categoryId,
      resolution: selectedResolution.value,
      count: 6
    })

    if (result.success && result.data && result.data.length > 0) {
      wallpaperBatch.value = result.data.map((wallpaper, index) => ({
        id: wallpaper.id || Date.now() + index,
        title: wallpaper.title || `${selectedCategory.value.name} ${index + 1}`,
        description: wallpaper.description || `精美的${selectedCategory.value.name}壁纸`,
        url: wallpaper.url,
        category: selectedCategory.value.name,
        tags: wallpaper.tags || [selectedCategory.value.name, '高清', '精美']
      }))
      showSuccess('壁纸批次加载成功！')
    } else {
      throw new Error(result.message || '获取壁纸批次失败')
    }
  } catch (error) {
    console.error('获取壁纸批次失败:', error)

    // 后端API失败时，使用本地模拟处理
    try {
      await simulateBatchGeneration(categoryId)
      showSuccess('壁纸批次加载成功！（本地处理）')
    } catch (localError) {
      showError('壁纸批次加载失败，请重试')
    }
  } finally {
    batchLoading.value = false
  }
}

// 本地模拟批次生成
const simulateBatchGeneration = async (categoryId = 'all') => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const batch = []
      const batchSize = 6

      for (let i = 0; i < batchSize; i++) {
        const randomId = Date.now() + i
        let imageUrl = `https://picsum.photos/${selectedResolution.value.width}/${selectedResolution.value.height}?random=${randomId}`

        // 根据分类添加特殊效果
        if (categoryId === 'abstract') {
          imageUrl += '&blur=1'
        } else if (categoryId === 'minimal') {
          imageUrl += '&grayscale'
        }

        batch.push({
          id: randomId,
          title: `${selectedCategory.value.name} ${i + 1}`,
          description: `精美的${selectedCategory.value.name}壁纸`,
          url: imageUrl,
          category: selectedCategory.value.name,
          tags: [selectedCategory.value.name, '高清', '精美']
        })
      }

      wallpaperBatch.value = batch
      resolve()
    }, 1000)
  })
}

const toggleFavorite = (id) => {
  if (favorites.value.includes(id)) {
    favorites.value = favorites.value.filter(fav => fav !== id)
  } else {
    favorites.value.push(id)
  }
}

const downloadWallpaper = (wallpaper) => {
  // 在微信小程序中，需要使用 uni.downloadFile 和 uni.saveImageToPhotosAlbum
  uni.showLoading({
    title: '下载中...'
  })

  uni.downloadFile({
    url: wallpaper.url,
    success: (res) => {
      if (res.statusCode === 200) {
        uni.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success: () => {
            uni.hideLoading()
            uni.showToast({
              title: '保存成功',
              icon: 'success',
              duration: 2000
            })
          },
          fail: (error) => {
            uni.hideLoading()
            console.error('保存失败:', error)
            uni.showToast({
              title: '保存失败',
              icon: 'error',
              duration: 2000
            })
          }
        })
      }
    },
    fail: (error) => {
      uni.hideLoading()
      console.error('下载失败:', error)
      uni.showToast({
        title: '下载失败',
        icon: 'error',
        duration: 2000
      })
    }
  })

  // 添加到下载历史
  const downloadItem = {
    ...wallpaper,
    downloadTime: new Date().toLocaleString()
  }

  downloadHistory.value.unshift(downloadItem)
  if (downloadHistory.value.length > 20) {
    downloadHistory.value = downloadHistory.value.slice(0, 20)
  }
}

// 修改获取分类图标的方法
const getCategoryIcon = (categoryId) => {
  const icons = {
    'all': '🖼️',
    'nature': '🌲',
    'city': '🏙️',
    'abstract': '🎨',
    'space': '🌌',
    'animal': '🐾',
    'tech': '💻',
    'art': '🎭',
    'minimal': '⚪'
  }
  return icons[categoryId] || '🖼️'
}

onMounted(() => {
  // 默认选中全部分类
  selectedCategory.value = categories[0]
  generateWallpaperBatch('all')
})
</script>

<style lang="scss" scoped>
.random-mobile-wallpaper {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 32rpx 24rpx;
}

.filter-section {
  margin-bottom: 32rpx;

  .filter-header {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;

    .filter-icon {
      font-size: 32rpx;
      color: #3b82f6;
      margin-right: 16rpx;
    }

    .filter-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .category-scroll {
    width: 100%;
    white-space: nowrap;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      bottom: -8rpx;
      height: 4rpx;
      background: #f0f0f0;
      border-radius: 2rpx;
    }

    .category-list {
      display: inline-flex;
      padding: 8rpx 4rpx;

      &::-webkit-scrollbar {
        display: none;
      }
    }

    ::-webkit-scrollbar {
      display: none;
    }

    .scroll-bar {
      position: absolute;
      left: 0;
      right: 0;
      bottom: -8rpx;
      height: 4rpx;
      background: #1a1a1a;
      border-radius: 2rpx;
      transform-origin: left;
      transition: transform 0.3s ease;
    }

    .category-btn {
      display: inline-flex;
      align-items: center;
      padding: 20rpx 32rpx;
      background: white;
      border-radius: 16rpx;
      border: 2rpx solid #f0f0f0;
      transition: all 0.3s ease;
      margin-right: 16rpx;

      &:last-child {
        margin-right: 4rpx;
      }

      .category-icon {
        font-size: 36rpx;
        margin-right: 16rpx;
      }

      .category-name {
        font-size: 28rpx;
        color: #333;
        white-space: nowrap;
      }

      &.active {
        background: #1a1a1a;
        border-color: #1a1a1a;

        .category-name {
          color: white;
        }
      }

      &:active {
        transform: scale(0.98);
      }
    }
  }
}

.wallpaper-section {
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 32rpx;

    .wallpaper-count {
      font-size: 28rpx;
      color: #666;
      margin-left: 8rpx;
    }
  }
}

.wallpaper-grid {
  display: flex;
  gap: 16rpx;
  align-items: flex-start;

  .wallpaper-column {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}

.wallpaper-card {
  position: relative;
  border-radius: 20rpx;
  overflow: hidden;
  background: #000;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
  }

  .wallpaper-img {
    width: 100%;
    height: auto;
    min-height: 300rpx;
    max-height: 500rpx;
    object-fit: cover;
    display: block;
    opacity: 0.95;
  }

  .wallpaper-overlay {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 24rpx;
    background: linear-gradient(to top, rgba(0,0,0,0.85) 0%, rgba(0,0,0,0.3) 60%, rgba(0,0,0,0) 100%);

    .wallpaper-info {
      margin-bottom: 12rpx;

      .wallpaper-title {
        font-size: 28rpx;
        font-weight: 600;
        color: white;
        display: block;
        margin-bottom: 6rpx;
        line-height: 1.3;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .wallpaper-resolution {
        font-size: 22rpx;
        color: rgba(255,255,255,0.7);
        display: block;
      }
    }

    .wallpaper-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8rpx;
      margin-top: 12rpx;

      .tag {
        padding: 6rpx 16rpx;
        background: rgba(255,255,255,0.2);
        border-radius: 20rpx;
        font-size: 20rpx;
        color: white;
        backdrop-filter: blur(4px);
        border: 1rpx solid rgba(255,255,255,0.1);
      }
    }
  }

  .action-buttons {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    display: flex;
    flex-direction: column;
    gap: 12rpx;

    .action-btn {
      width: 64rpx;
      height: 64rpx;
      border-radius: 50%;
      background: rgba(0,0,0,0.4);
      backdrop-filter: blur(8rpx);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      border: 1rpx solid rgba(255,255,255,0.2);

      .action-icon {
        font-size: 28rpx;
        color: white;
        font-weight: bold;
      }

      &.favorite {
        &.active {
          background: rgba(239,68,68,0.9);
          border-color: rgba(239,68,68,0.5);
        }
      }

      &.download {
        &:active {
          background: rgba(34,197,94,0.9);
          border-color: rgba(34,197,94,0.5);
        }
      }

      &:active {
        transform: scale(0.9);
      }
    }
  }
}

// 暗黑模式适配
@media (prefers-color-scheme: dark) {
  .random-mobile-wallpaper {
    background: #18181c;
  }

  .filter-section {
    .filter-title {
      color: #fafafa;
    }

    .category-scroll {
      &::after {
        background: #2a2a2a;
      }

      .scroll-bar {
        background: #3b82f6;
      }
    }

    .category-btn {
      background: #23232a;
      border-color: #2a2a2a;

      .category-name {
        color: #fafafa;
      }

      &.active {
        background: #3b82f6;
        border-color: #3b82f6;
      }
    }
  }

  .section-title {
    color: #fafafa;

    .wallpaper-count {
      color: #bdbdbd;
    }
  }
}
</style>
