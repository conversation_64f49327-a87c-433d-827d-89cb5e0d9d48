<template>
  <view class="bing-wallpaper-page">
    <!-- 主要内容区域 -->
    <view class="content">
      <!-- 今日壁纸 -->
      <view v-if="currentWallpaper" class="wallpaper-card main-card">
        <view class="card-header">
          <text class="card-icon">📅</text>
          <text class="card-title">今日壁纸 - {{ currentWallpaper.date }}</text>
          <!-- 刷新按钮，极简文字，右上角 -->
          <button class="refresh-btn-minimal" @tap="refreshWallpapers" :disabled="loading">
            <text class="refresh-btn-text">刷新</text>
          </button>
        </view>
        <view class="card-content">
          <view class="wallpaper-container">
            <image
              :src="currentWallpaper.url"
              :alt="currentWallpaper.title"
              class="wallpaper-image"
              mode="aspectFill"
              @error="onImageError"
              @tap="showWallpaperDetail(currentWallpaper)"
            />
            <!-- 下载按钮，极简icon，右上角浮动 -->
            <button class="download-btn-minimal" @tap.stop="downloadWallpaper(currentWallpaper)">
              <svg class="download-svg" viewBox="0 0 24 24" width="32" height="32" fill="none" stroke="currentColor" stroke-width="2.4" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                <polyline points="7 10 12 15 17 10"/>
                <line x1="12" y1="15" x2="12" y2="3"/>
              </svg>
                </button>
          </view>
          <view class="wallpaper-info">
            <text class="wallpaper-title">{{ currentWallpaper.title }}</text>
            <text class="wallpaper-description">{{ currentWallpaper.description }}</text>
            <view class="wallpaper-location">
              <svg class="location-svg" viewBox="0 0 24 24" width="18" height="18" fill="none" stroke="#1976d2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="10" r="3"/><path d="M12 2a8 8 0 0 1 8 8c0 7-8 12-8 12S4 17 4 10a8 8 0 0 1 8-8z"/></svg>
              <text class="location-link" @tap="onLocationClick(currentWallpaper.location)">{{ currentWallpaper.location }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 最近壁纸 -->
      <view class="wallpaper-card recent-card">
        <view class="card-header">
          <text class="card-title">最近壁纸</text>
        </view>
        <view class="card-content">
          <view class="recent-list">
            <view
              v-for="(wallpaper, index) in recentWallpapers.slice(1)"
              :key="index"
              class="recent-item"
              @tap="showWallpaperDetail(wallpaper)"
            >
              <image
                :src="wallpaper.url"
                :alt="wallpaper.title"
                class="recent-image"
                mode="aspectFill"
                @error="onImageError"
              />
              <view class="recent-info">
                <text class="recent-title">{{ wallpaper.title }}</text>
                <text class="recent-date">{{ wallpaper.date }}</text>
                <view class="recent-location">
                  <svg class="location-svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="#1976d2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="10" r="3"/><path d="M12 2a8 8 0 0 1 8 8c0 7-8 12-8 12S4 17 4 10a8 8 0 0 1 8-8z"/></svg>
                  <text class="location-link" @tap.stop="onLocationClick(wallpaper.location)">{{ wallpaper.location }}</text>
                </view>
              </view>
              <!-- 明显的下载按钮 -->
              <button
                @tap.stop="downloadWallpaper(wallpaper, '1920x1080')"
                class="download-btn-strong"
                aria-label="下载"
              >
                <svg class="download-svg" viewBox="0 0 24 24" width="36" height="36" fill="none" stroke="currentColor" stroke-width="2.4" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                  <polyline points="7 10 12 15 17 10"/>
                  <line x1="12" y1="15" x2="12" y2="3"/>
                </svg>
              </button>
            </view>
          </view>
        </view>
      </view>

      <!-- 使用说明 -->
      <view class="wallpaper-card usage-card">
        <view class="card-header">
          <text class="card-title">使用说明</text>
        </view>
        <view class="card-content">
          <view class="usage-list">
            <view v-for="(tip, index) in usageTips" :key="index" class="usage-item">
              <text class="usage-bullet">•</text>
              <text class="usage-text">{{ tip }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 壁纸详情弹窗 -->
    <view v-if="showDetail" class="detail-mask" @tap.self="closeDetail">
      <view class="detail-dialog">
        <view class="detail-header">
          <text class="detail-title">{{ detailWallpaper.title }}</text>
          <text class="detail-close" @tap="closeDetail">×</text>
        </view>
        <view class="detail-content">
          <view class="detail-main-content">
            <image
              :src="detailWallpaper.url"
              :alt="detailWallpaper.title"
              class="detail-image"
              mode="aspectFit"
            />
            <view class="detail-info">
              <text class="detail-description">{{ detailWallpaper.description }}</text>
              <view class="detail-location">
                <svg class="location-svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="#1976d2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="10" r="3"/><path d="M12 2a8 8 0 0 1 8 8c0 7-8 12-8 12S4 17 4 10a8 8 0 0 1 8-8z"/></svg>
                <text class="location-link" @tap.stop="onLocationClick(detailWallpaper.location)">{{ detailWallpaper.location }}</text>
              </view>
              <text class="detail-date">{{ detailWallpaper.date }}</text>
            </view>
          </view>
          <view class="detail-actions">
            <button
              v-for="resolution in resolutions"
              :key="resolution"
              @tap="downloadWallpaper(detailWallpaper, resolution)"
              class="detail-download-btn"
            >
              <svg class="download-svg" viewBox="0 0 24 24" width="22" height="22" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                <polyline points="7 10 12 15 17 10"/>
                <line x1="12" y1="15" x2="12" y2="3"/>
              </svg>
              <text class="download-text">{{ resolution }}</text>
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 下载选项弹窗 -->
    <view v-if="showDownloadMenu" class="download-mask" @tap.self="closeDownloadMenu">
      <view class="download-dialog">
        <view class="download-header">
          <text class="download-title">选择下载分辨率</text>
          <text class="download-close" @tap="closeDownloadMenu">×</text>
        </view>
        <view class="download-content">
          <button
            v-for="resolution in resolutions"
            :key="resolution"
            @tap="confirmDownload(resolution)"
            class="resolution-btn"
          >
            <text class="resolution-text">{{ resolution }}</text>
            <text class="resolution-desc">{{ getResolutionDesc(resolution) }}</text>
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentWallpaper: null,
      recentWallpapers: [],
      loading: false,
      showDetail: false,
      detailWallpaper: null,
      showDownloadMenu: false,
      downloadWallpaperTarget: null,
      
      resolutions: ['1920x1080', '2560x1440', '3840x2160'],
      
      usageTips: [
        '每日更新Bing搜索首页的精美壁纸',
        '支持多种分辨率下载，适配不同设备',
        '所有图片均为高质量摄影作品',
        '可查看最近一周的历史壁纸',
        '一键下载，方便设置为桌面壁纸'
      ]
    }
  },
  
  onLoad() {
    this.initializeWallpapers()
  },
  
  methods: {
    // 初始化壁纸数据
    initializeWallpapers() {
      const mockWallpapers = this.generateMockWallpapers()
      this.currentWallpaper = mockWallpapers[0]
      this.recentWallpapers = mockWallpapers
    },
    
    // 生成模拟壁纸数据
    generateMockWallpapers() {
  const mockData = [
    {
      date: new Date().toISOString().split('T')[0],
      title: "阿尔卑斯山的日出",
      description: "瑞士阿尔卑斯山脉在晨曦中展现出壮丽的景色，金色的阳光洒向雪山顶峰。",
      url: "https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=1920&h=1080&fit=crop",
      location: "瑞士阿尔卑斯山"
    },
    {
      date: new Date(Date.now() - 86400000).toISOString().split('T')[0],
      title: "挪威峡湾风光",
      description: "挪威的峡湾在蓝色天空下展现出令人惊叹的自然美景。",
      url: "https://images.unsplash.com/photo-1506744038136-46273834b3fb?w=1920&h=1080&fit=crop",
      location: "挪威峡湾"
    },
    {
      date: new Date(Date.now() - 172800000).toISOString().split('T')[0],
      title: "樱花盛开的季节",
      description: "春天的樱花树下，粉色的花瓣如雪花般飘落。",
      url: "https://images.unsplash.com/photo-1522383225653-ed111181a951?w=1920&h=1080&fit=crop",
      location: "日本京都"
    },
    {
      date: new Date(Date.now() - 259200000).toISOString().split('T')[0],
      title: "撒哈拉沙漠的星空",
      description: "在撒哈拉沙漠的夜晚，满天繁星点亮了整个天空。",
      url: "https://images.unsplash.com/photo-1470813740244-df37b8c1edcb?w=1920&h=1080&fit=crop",
      location: "撒哈拉沙漠"
    },
    {
      date: new Date(Date.now() - 345600000).toISOString().split('T')[0],
      title: "冰岛的蓝色冰川",
      description: "冰岛瓦特纳冰川国家公园的蓝色冰洞，展现大自然的神奇魅力。",
      url: "https://images.unsplash.com/photo-1531366936337-7c912a4589a7?w=1920&h=1080&fit=crop",
      location: "冰岛瓦特纳冰川"
    },
    {
      date: new Date(Date.now() - 432000000).toISOString().split('T')[0],
      title: "托斯卡纳的田园风光",
      description: "意大利托斯卡纳地区的丘陵地带，绿色的葡萄园延伸到地平线。",
      url: "https://images.unsplash.com/photo-1523712999610-f77fbcfc3843?w=1920&h=1080&fit=crop",
      location: "意大利托斯卡纳"
    },
    {
      date: new Date(Date.now() - 518400000).toISOString().split('T')[0],
      title: "马尔代夫的蓝色泻湖",
      description: "马尔代夫清澈的海水和白色的沙滩构成了完美的热带天堂。",
      url: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&h=1080&fit=crop",
      location: "马尔代夫"
    }
  ]
  return mockData
    },
    
    // 返回上一页
    navigateBack() {
      uni.navigateBack()
    },
    
    // 刷新壁纸
    refreshWallpapers() {
      if (this.loading) return
      this.loading = true
      setTimeout(() => {
        const newWallpapers = this.generateMockWallpapers()
        // 随机选取一张作为今日壁纸
        const idx = Math.floor(Math.random() * newWallpapers.length)
        this.currentWallpaper = newWallpapers[idx]
        this.recentWallpapers = newWallpapers
        this.loading = false
        uni.showToast({
          title: '刷新成功',
          icon: 'success',
          duration: 2000
        })
      }, 1000)
    },
    
    // 显示下载选项
    showDownloadOptions(wallpaper) {
      this.downloadWallpaperTarget = wallpaper
      this.showDownloadMenu = true
    },
    
    // 关闭下载菜单
    closeDownloadMenu() {
      this.showDownloadMenu = false
      this.downloadWallpaperTarget = null
    },
    
    // 确认下载
    confirmDownload(resolution) {
      if (this.downloadWallpaperTarget) {
        this.downloadWallpaper(this.downloadWallpaperTarget, resolution)
      }
      this.closeDownloadMenu()
    },
    
    // 下载壁纸
    downloadWallpaper(wallpaper, resolution = '1920x1080') {
      // 在微信小程序中，需要使用 uni.downloadFile 和 uni.saveImageToPhotosAlbum
      uni.showLoading({
        title: '下载中...'
      })
      
      uni.downloadFile({
        url: wallpaper.url,
        success: (res) => {
          if (res.statusCode === 200) {
            uni.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                uni.hideLoading()
                uni.showToast({
                  title: '保存成功',
                  icon: 'success',
                  duration: 2000
                })
              },
              fail: (error) => {
                uni.hideLoading()
                console.error('保存失败:', error)
                uni.showToast({
                  title: '保存失败',
                  icon: 'error',
                  duration: 2000
                })
              }
            })
          }
        },
        fail: (error) => {
          uni.hideLoading()
          console.error('下载失败:', error)
          uni.showToast({
            title: '下载失败',
            icon: 'error',
            duration: 2000
          })
        }
      })
    },
    
    // 显示壁纸详情
    showWallpaperDetail(wallpaper) {
      this.detailWallpaper = wallpaper
      this.showDetail = true
    },
    
    // 关闭详情
    closeDetail() {
      this.showDetail = false
      this.detailWallpaper = null
    },
    
    // 获取分辨率描述
    getResolutionDesc(resolution) {
      const descriptions = {
        '1920x1080': '全高清',
        '2560x1440': '2K',
        '3840x2160': '4K'
      }
      return descriptions[resolution] || ''
    },
    
    // 图片加载错误处理
    onImageError(e) {
      console.error('图片加载失败:', e)
    },
    
    // 处理位置点击
    onLocationClick(location) {
      // 实现位置点击逻辑
    }
  }
}
</script>

<style lang="scss" scoped>
.bing-wallpaper-page {
  min-height: 100vh;
  background: #ffffff;
  display: flex;
  flex-direction: column;
}

// 主要内容区域
.content {
  flex: 1;
  padding: 32rpx;
  background: #f8f9fa;
}

// 壁纸卡片
.wallpaper-card {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  
  &:hover {
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
    transform: translateY(-2rpx);
  }
}

.card-header {
  padding: 32rpx 32rpx 0;
  display: flex;
  align-items: center;
  gap: 12rpx;
  justify-content: space-between;
}

.card-icon {
  font-size: 28rpx;
  color: #ff6b35;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.card-content {
  padding: 24rpx 32rpx 32rpx;
}

// 今日壁纸样式
.main-card {
  .wallpaper-container {
    position: relative;
    border-radius: 12rpx;
    overflow: hidden;
    aspect-ratio: 16/9;
    background: #f8f9fa;
  }
  
  .wallpaper-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .wallpaper-overlay {
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
    opacity: 0;
    transition: opacity 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    
    &:hover {
  opacity: 1;
    }
  }
  
  .download-options {
    display: flex;
    gap: 16rpx;
  }
  
  .download-btn {
    background: rgba(255, 255, 255, 0.9);
    color: #1a1a1a;
    border: none;
    border-radius: 8rpx;
    padding: 12rpx 16rpx;
    font-size: 24rpx;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8rpx;
    transition: all 0.15s;
    backdrop-filter: blur(8rpx);
    
    &:hover {
      background: #ffffff;
      transform: translateY(-2rpx);
    }
  }
  
  .download-icon {
    font-size: 24rpx;
  }
  
  .download-text {
    font-size: 24rpx;
    font-weight: 500;
  }
  
  .wallpaper-info {
    margin-top: 24rpx;
  }
  
  .wallpaper-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #1a1a1a;
    display: block;
    margin-bottom: 12rpx;
  }
  
  .wallpaper-description {
    font-size: 28rpx;
    color: #6c757d;
    line-height: 1.5;
    display: block;
    margin-bottom: 16rpx;
  }
  
  .wallpaper-location {
    display: flex;
    align-items: center;
    gap: 8rpx;
  }
  
  .location-icon {
    font-size: 24rpx;
    color: #007bff;
  }
  
  .location-text {
    font-size: 26rpx;
    color: #007bff;
    font-weight: 500;
  }
}

// 最近壁纸样式
.recent-card {
  .recent-list {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
  }
  
  .recent-item {
    display: flex;
    align-items: center;
    gap: 16rpx;
    padding: 16rpx;
    border-radius: 12rpx;
    border: 1rpx solid #f0f0f0;
    transition: all 0.15s;
    cursor: pointer;
    background: #fff;
    position: relative;
    
    &:hover {
      background: #f8f9fa;
      border-color: #dee2e6;
    }
    
    &:active {
      transform: scale(0.98);
    }
  }
  
  .recent-image {
    width: 120rpx;
    height: 120rpx;
    border-radius: 8rpx;
    object-fit: cover;
    background: #f8f9fa;
    flex-shrink: 0;
  }
  
  .recent-info {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 8rpx;
  }
  
  .recent-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #1a1a1a;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .recent-date {
    font-size: 24rpx;
    color: #6c757d;
  }
  
  .recent-location {
    display: flex;
    align-items: center;
    gap: 6rpx;
    margin-top: 2rpx;
  }
  
  .download-btn-minimal {
    margin-left: auto;
  }
}

// 使用说明样式
.usage-card {
  background: #f8f9fa;
  border: 1rpx solid #e9ecef;
  
  .usage-list {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
  }
  
  .usage-item {
    display: flex;
    align-items: flex-start;
    gap: 12rpx;
  }
  
  .usage-bullet {
    color: #6c757d;
    font-size: 24rpx;
    margin-top: 4rpx;
    flex-shrink: 0;
  }
  
  .usage-text {
    font-size: 28rpx;
    color: #495057;
    line-height: 1.5;
    flex: 1;
  }
}

// 弹窗样式
.detail-mask, .download-mask {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(4rpx);
}

.detail-dialog {
  border-radius: 20rpx;
  box-shadow: 0 12rpx 40rpx rgba(25, 118, 210, 0.10), 0 2rpx 8rpx rgba(0,0,0,0.04);
  background: #fff;
  max-width: 92vw;
  padding: 0;
}

.detail-header {
  padding: 36rpx 40rpx 0 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #f0f0f0;
  background: #fafbfc;
}

.detail-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #1a1a1a;
}

.detail-close {
  font-size: 40rpx;
  color: #b0b8c7;
  font-weight: 400;
  cursor: pointer;
  transition: color 0.15s;
  &:hover { color: #1976d2; }
}

.detail-content {
  padding: 40rpx 0 32rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.detail-main-content {
  max-width: 520rpx;
  width: 100%;
  margin: 0 auto;
}

.detail-image {
  width: 100%;
  max-width: 520rpx;
  max-height: 320rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(25, 118, 210, 0.08);
  background: #f6f8fa;
  object-fit: cover;
  margin-bottom: 32rpx;
}

.detail-info {
  margin-bottom: 32rpx;
  text-align: left;
}

.detail-description {
  font-size: 28rpx;
  color: #5a6270;
  line-height: 1.7;
  margin-bottom: 18rpx;
}

.detail-location {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 8rpx;
}

.location-link {
  font-size: 22rpx;
  color: #1976d2;
  font-weight: 400;
  margin-left: 2rpx;
  cursor: pointer;
  text-decoration: none;
  &:hover { text-decoration: underline; color: #1251a3; }
}

.detail-date {
  font-size: 22rpx;
  color: #b0b8c7;
  margin-bottom: 0;
}

.detail-actions {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 12rpx;
  justify-content: center;
  margin-top: 12rpx;
  overflow-x: hidden;
  padding: 0 32rpx 8rpx 32rpx;
  margin-left: 0;
  margin-right: 0;
}

.detail-download-btn {
  flex: 1 1 0;
  min-width: 0;
  height: 64rpx;
  font-size: 28rpx;
  font-weight: 600;
  border-radius: 16rpx;
  border: 2rpx solid #e3eaf5;
  background: #fff;
  color: #1976d2;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  transition: all 0.18s cubic-bezier(0.2,0,0.1,1);
  box-shadow: 0 2rpx 8rpx rgba(25, 118, 210, 0.06);
  margin: 0;
  padding: 0 12rpx;
}

.download-svg {
  width: 32rpx;
  height: 32rpx;
  stroke: currentColor;
  display: block;
  stroke-width: 2.2;
}

.download-btn-strong {
  background: rgba(25, 118, 210, 0.10);
  border: 1rpx solid #e3eaf5;
  box-shadow: none;
  padding: 0;
  margin: 0 24rpx 0 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition:
    color 0.15s,
    background 0.18s cubic-bezier(0.2,0,0.1,1),
    box-shadow 0.18s cubic-bezier(0.2,0,0.1,1),
    border-color 0.18s cubic-bezier(0.2,0,0.1,1),
    transform 0.18s cubic-bezier(0.2,0,0.1,1);
  color: #1565c0;
  border-radius: 50%;
  width: 56rpx;
  height: 56rpx;
  position: relative;
  &:hover {
    background: rgba(25, 118, 210, 0.18);
    color: #0d3570;
    border-color: #b6d0f7;
    box-shadow: 0 4rpx 16rpx rgba(25, 118, 210, 0.13);
    transform: scale(1.10);
  }
  &:active {
    color: #0a2540;
    background: rgba(25, 118, 210, 0.22);
    border-color: #90b4e8;
    transform: scale(0.97);
  }
}

.download-svg {
  width: 36rpx;
  height: 36rpx;
  stroke: currentColor;
  display: block;
  stroke-width: 2.4;
}

// 响应式设计
@media (max-width: 750rpx) {
  .content {
    padding: 24rpx;
  }
  
  .card-header {
    padding: 24rpx 24rpx 0;
  }
  
  .card-content {
    padding: 16rpx 24rpx 24rpx;
  }
  
  .download-options {
    flex-direction: column;
    gap: 12rpx;
  }
  
  .recent-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12rpx;
  }
  
  .recent-image {
    width: 100%;
    height: 200rpx;
  }
  
  .detail-actions {
    /* flex-direction: column; */
    flex-direction: row;
    flex-wrap: nowrap;
    gap: 24rpx;
    justify-content: center;
  }
  
  .detail-download-btn {
    width: 100%;
    justify-content: center;
  }
}

.refresh-btn-minimal {
  background: none;
  border: none;
  box-shadow: none;
  padding: 0 16rpx;
  margin-right: 4rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 56rpx;
  min-width: 72rpx;
  cursor: pointer;
  color: #222;
  border-radius: 8rpx;
  font-size: 32rpx;
  transition: color 0.15s, transform 0.15s;
  &:hover {
    color: #1976d2;
    transform: scale(1.06);
  }
  &:active {
    color: #1251a3;
    transform: scale(0.97);
  }
}

.refresh-btn-text {
  font-size: 32rpx;
  font-weight: 500;
  color: inherit;
  line-height: 56rpx;
  padding: 0;
  margin: 0;
  position: static;
}
</style> 