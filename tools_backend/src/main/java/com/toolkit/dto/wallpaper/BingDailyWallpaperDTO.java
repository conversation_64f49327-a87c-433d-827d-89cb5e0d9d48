package com.toolkit.dto.wallpaper;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 随机手机壁纸请求DTO
 */
@Data
@Schema(description = "随机手机壁纸请求参数")
public class RandomMobileWallpaperDTO {

    @Schema(description = "分类", example = "nature")
    private String category = "all";

    @Schema(description = "分辨率宽度", example = "750")
    private Integer width = 750;

    @Schema(description = "分辨率高度", example = "1334")
    private Integer height = 1334;

    @Schema(description = "数量", example = "6")
    private Integer count = 6;
}