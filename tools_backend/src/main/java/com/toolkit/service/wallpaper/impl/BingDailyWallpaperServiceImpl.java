package com.toolkit.service.wallpaper.impl;

import com.toolkit.dto.wallpaper.RandomMobileWallpaperDTO;
import com.toolkit.service.wallpaper.RandomMobileWallpaperService;
import com.toolkit.service.cache.DataCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;

/**
 * 随机手机壁纸服务实现类
 * 生成随机手机壁纸数据，支持多种分类和分辨率
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RandomMobileWallpaperServiceImpl implements RandomMobileWallpaperService {

    private final RestTemplate restTemplate = new RestTemplate();
    private final DataCacheService cacheService;

    private static final String CACHE_KEY = "random_mobile_wallpaper";

    @Override
    public Object process(RandomMobileWallpaperDTO request) {
        log.info("随机手机壁纸查询请求: {}", request);

        try {
            // 生成随机手机壁纸数据
            List<Map<String, Object>> wallpapers = generateRandomMobileWallpapers(request);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", wallpapers);
            result.put("message", "获取成功");
            result.put("total", wallpapers.size());

            return result;
        } catch (Exception e) {
            log.error("随机手机壁纸生成失败: {}", e.getMessage());

            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("data", new ArrayList<>());
            result.put("message", "生成失败：" + e.getMessage());
            result.put("total", 0);

            return result;
        }
    }

    /**
     * 生成随机手机壁纸数据
     */
    private List<Map<String, Object>> generateRandomMobileWallpapers(RandomMobileWallpaperDTO request) {
        List<Map<String, Object>> wallpapers = new ArrayList<>();

        for (int i = 0; i < request.getCount(); i++) {
            Map<String, Object> wallpaper = new HashMap<>();

            long randomId = System.currentTimeMillis() + i;
            String imageUrl = String.format("https://picsum.photos/%d/%d?random=%d",
                request.getWidth(), request.getHeight(), randomId);

            // 根据分类添加特殊效果
            if ("abstract".equals(request.getCategory())) {
                imageUrl += "&blur=1";
            } else if ("minimal".equals(request.getCategory())) {
                imageUrl += "&grayscale";
            }

            wallpaper.put("id", randomId);
            wallpaper.put("title", getCategoryName(request.getCategory()) + " " + (i + 1));
            wallpaper.put("description", "精美的" + getCategoryName(request.getCategory()) + "手机壁纸");
            wallpaper.put("url", imageUrl);
            wallpaper.put("category", getCategoryName(request.getCategory()));
            wallpaper.put("tags", Arrays.asList(getCategoryName(request.getCategory()), "高清", "精美", "手机"));
            wallpaper.put("resolution", request.getWidth() + "×" + request.getHeight());

            wallpapers.add(wallpaper);
        }

        return wallpapers;
    }

    /**
     * 获取分类名称
     */
    private String getCategoryName(String categoryId) {
        Map<String, String> categoryNames = new HashMap<>();
        categoryNames.put("all", "全部");
        categoryNames.put("nature", "自然风光");
        categoryNames.put("city", "城市建筑");
        categoryNames.put("abstract", "抽象艺术");
        categoryNames.put("space", "宇宙星空");
        categoryNames.put("animal", "动物世界");
        categoryNames.put("tech", "科技数码");
        categoryNames.put("art", "艺术创作");
        categoryNames.put("minimal", "简约风格");

        return categoryNames.getOrDefault(categoryId, "全部");
    }
}